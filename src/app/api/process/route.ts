import { NextRequest, NextResponse } from 'next/server'
import { Readability } from '@mozilla/readability'
import { JSDOM } from 'jsdom'
import OpenAI from 'openai'

// 初始化OpenAI客户端
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

// 从URL抓取内容
async function fetchUrlContent(url: string) {
  try {
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    })
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`)
    }
    
    const html = await response.text()
    const dom = new JSDOM(html, { url })
    const reader = new Readability(dom.window.document)
    const article = reader.parse()
    
    if (!article) {
      throw new Error('无法解析网页内容')
    }
    
    return {
      title: article.title || '无标题',
      content: article.content || '',
      textContent: article.textContent || ''
    }
  } catch (error) {
    console.error('抓取URL内容失败:', error)
    throw new Error('无法获取网页内容，请检查URL是否正确')
  }
}

// 生成AI笔记
async function generateAINote(content: string, title: string) {
  try {
    const systemPrompt = process.env.AI_SYSTEM_PROMPT || `你是一个专业的知识提炼助手。请将用户提供的内容转换为结构化的笔记，包含：1. 核心观点总结 2. 关键信息提取 3. 实用价值分析。请用Markdown格式输出，保持简洁明了。`
    
    const response = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || 'gpt-4',
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: `请为以下内容生成结构化的AI笔记：\n\n标题：${title}\n\n内容：${content}`
        }
      ],
      max_tokens: parseInt(process.env.OPENAI_MAX_TOKENS || '2000'),
      temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.7')
    })
    
    return response.choices[0]?.message?.content || '无法生成AI笔记'
  } catch (error) {
    console.error('生成AI笔记失败:', error)
    return '抱歉，AI笔记生成失败，请稍后重试。'
  }
}

export async function POST(request: NextRequest) {
  try {
    const { input, type } = await request.json()
    
    if (!input || !type) {
      return NextResponse.json(
        { error: '缺少必要参数' },
        { status: 400 }
      )
    }
    
    let title: string
    let content: string
    let textContent: string
    
    if (type === 'url') {
      // 处理URL
      const urlData = await fetchUrlContent(input)
      title = urlData.title
      content = urlData.content
      textContent = urlData.textContent
    } else {
      // 处理文本
      title = '文本内容'
      content = `<div class="text-content">${input.replace(/\n/g, '<br>')}</div>`
      textContent = input
    }
    
    // 生成AI笔记
    const aiNote = await generateAINote(textContent, title)
    
    return NextResponse.json({
      title,
      content,
      aiNote,
      success: true
    })
    
  } catch (error) {
    console.error('处理请求失败:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '处理失败',
        success: false 
      },
      { status: 500 }
    )
  }
}
