import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    
    if (!id) {
      return NextResponse.json(
        { error: '缺少会话ID' },
        { status: 400 }
      )
    }
    
    // 获取特定会话的完整信息
    const session = await prisma.session.findUnique({
      where: {
        id: id
      }
    })
    
    if (!session) {
      return NextResponse.json(
        { error: '会话不存在' },
        { status: 404 }
      )
    }
    
    return NextResponse.json({
      session,
      success: true
    })
    
  } catch (error) {
    console.error('获取会话详情失败:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '获取会话失败',
        success: false 
      },
      { status: 500 }
    )
  } finally {
    await prisma.$disconnect()
  }
}
