import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    const { title, sourceType, sourceData, originalContent, aiNoteMarkdown } = await request.json()
    
    if (!title || !sourceType || !sourceData || !originalContent || !aiNoteMarkdown) {
      return NextResponse.json(
        { error: '缺少必要参数' },
        { status: 400 }
      )
    }
    
    // 保存到数据库
    const session = await prisma.session.create({
      data: {
        title,
        sourceType,
        sourceData,
        originalContent,
        aiNoteMarkdown
      }
    })
    
    return NextResponse.json({
      id: session.id,
      success: true,
      message: '保存成功'
    })
    
  } catch (error) {
    console.error('保存会话失败:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '保存失败',
        success: false 
      },
      { status: 500 }
    )
  } finally {
    await prisma.$disconnect()
  }
}
