import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    // 获取所有会话，按创建时间倒序
    const sessions = await prisma.session.findMany({
      select: {
        id: true,
        title: true,
        sourceType: true,
        sourceData: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })
    
    return NextResponse.json({
      sessions,
      success: true
    })
    
  } catch (error) {
    console.error('获取会话列表失败:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '获取列表失败',
        success: false 
      },
      { status: 500 }
    )
  } finally {
    await prisma.$disconnect()
  }
}
