import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    // 获取所有会话
    const sessions = await prisma.session.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    })
    
    if (sessions.length === 0) {
      return NextResponse.json({
        message: '没有可同步的内容',
        success: true
      })
    }
    
    // 检查FastGPT配置
    const fastgptUrl = process.env.FASTGPT_API_URL
    const fastgptKey = process.env.FASTGPT_API_KEY
    
    if (!fastgptUrl || !fastgptKey) {
      return NextResponse.json(
        { error: 'FastGPT配置不完整，请检查环境变量' },
        { status: 400 }
      )
    }
    
    // 准备同步数据
    const syncData = sessions.map(session => ({
      id: session.id,
      title: session.title,
      content: `${session.originalContent}\n\n## AI笔记\n${session.aiNoteMarkdown}`,
      source: session.sourceData,
      createdAt: session.createdAt
    }))
    
    try {
      // 调用FastGPT API（这里需要根据实际的FastGPT API接口调整）
      const response = await fetch(`${fastgptUrl}/api/sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${fastgptKey}`
        },
        body: JSON.stringify({
          data: syncData,
          source: '沉淀应用'
        })
      })
      
      if (!response.ok) {
        throw new Error(`FastGPT API错误: ${response.status}`)
      }
      
      const result = await response.json()
      
      return NextResponse.json({
        message: `成功同步 ${sessions.length} 条记录到FastGPT`,
        syncedCount: sessions.length,
        fastgptResponse: result,
        success: true
      })
      
    } catch (fastgptError) {
      console.error('FastGPT同步失败:', fastgptError)
      return NextResponse.json(
        { 
          error: `FastGPT同步失败: ${fastgptError instanceof Error ? fastgptError.message : '未知错误'}`,
          success: false 
        },
        { status: 500 }
      )
    }
    
  } catch (error) {
    console.error('同步准备失败:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '同步失败',
        success: false 
      },
      { status: 500 }
    )
  } finally {
    await prisma.$disconnect()
  }
}
