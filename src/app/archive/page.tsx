'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { useAppStore } from '@/lib/store'
import { ArrowLeft, ExternalLink, MessageCircle, RefreshCw } from 'lucide-react'

interface Session {
  id: string
  title: string
  sourceType: string
  sourceData: string
  createdAt: string
}

const ArchivePage: React.FC = () => {
  const { addTab } = useAppStore()
  const [sessions, setSessions] = useState<Session[]>([])
  const [loading, setLoading] = useState(true)
  const [syncing, setSyncing] = useState(false)

  // 获取会话列表
  const fetchSessions = async () => {
    try {
      const response = await fetch('/api/archive/list')
      if (!response.ok) {
        throw new Error('获取列表失败')
      }
      const data = await response.json()
      setSessions(data.sessions || [])
    } catch (error) {
      console.error('获取会话列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 加载历史会话
  const loadSession = async (sessionId: string) => {
    try {
      const response = await fetch(`/api/archive/${sessionId}`)
      if (!response.ok) {
        throw new Error('获取会话失败')
      }
      const data = await response.json()
      const session = data.session

      // 清空当前标签页并创建新标签页
      const tabId = addTab({
        title: session.title,
        sourceType: session.sourceType,
        sourceData: session.sourceData,
        originalContent: session.originalContent,
        aiNoteMarkdown: session.aiNoteMarkdown,
        isLoading: false
      })

      // 跳转到主页
      window.location.href = '/'
    } catch (error) {
      console.error('加载会话失败:', error)
    }
  }

  // 同步到FastGPT
  const syncToFastGPT = async () => {
    setSyncing(true)
    try {
      const response = await fetch('/api/sync/fastgpt', {
        method: 'POST'
      })
      
      if (!response.ok) {
        throw new Error('同步失败')
      }
      
      const data = await response.json()
      alert(`同步成功！${data.message}`)
    } catch (error) {
      console.error('同步失败:', error)
      alert('同步失败，请稍后重试')
    } finally {
      setSyncing(false)
    }
  }

  // 与知识库对话
  const openFastGPTChat = () => {
    const fastgptUrl = process.env.NEXT_PUBLIC_FASTGPT_CHAT_URL || 'https://fastgpt.example.com/chat'
    window.open(fastgptUrl, '_blank')
  }

  useEffect(() => {
    fetchSessions()
  }, [])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-6xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link 
                href="/"
                className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft size={20} />
                <span>返回主页</span>
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <h1 className="text-2xl font-bold text-gray-900">知识库</h1>
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={syncToFastGPT}
                disabled={syncing}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                <RefreshCw size={16} className={syncing ? 'animate-spin' : ''} />
                <span>{syncing ? '同步中...' : '同步到FastGPT'}</span>
              </button>
              
              <button
                onClick={openFastGPTChat}
                className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <MessageCircle size={16} />
                <span>与知识库对话</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-6xl mx-auto px-6 py-8">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-gray-600">加载中...</p>
            </div>
          </div>
        ) : sessions.length === 0 ? (
          <div className="text-center py-16">
            <div className="w-24 h-24 bg-gray-100 rounded-full mx-auto mb-6 flex items-center justify-center">
              <span className="text-4xl">📚</span>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">知识库为空</h2>
            <p className="text-gray-600 mb-6">
              还没有保存任何内容。开始处理一些网页或文本，然后保存到知识库吧！
            </p>
            <Link
              href="/"
              className="inline-flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <span>开始使用</span>
            </Link>
          </div>
        ) : (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <p className="text-gray-600">
                共 {sessions.length} 条记录
              </p>
            </div>

            <div className="grid gap-4">
              {sessions.map((session) => (
                <div
                  key={session.id}
                  className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => loadSession(session.id)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                        {session.title}
                      </h3>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                        <span className="flex items-center space-x-1">
                          <span className={`w-2 h-2 rounded-full ${
                            session.sourceType === 'url' ? 'bg-blue-500' : 'bg-green-500'
                          }`}></span>
                          <span>{session.sourceType === 'url' ? '网页' : '文本'}</span>
                        </span>
                        
                        <span>
                          {new Date(session.createdAt).toLocaleDateString('zh-CN', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </span>
                      </div>
                      
                      {session.sourceType === 'url' && (
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <ExternalLink size={14} />
                          <span className="truncate">{session.sourceData}</span>
                        </div>
                      )}
                    </div>
                    
                    <div className="ml-4">
                      <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                        <span className="text-xl">
                          {session.sourceType === 'url' ? '🌐' : '📝'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </main>
    </div>
  )
}

export default ArchivePage
