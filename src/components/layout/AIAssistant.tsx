'use client'

import React, { useState } from 'react'
import ReactMarkdown from 'react-markdown'
import { useAppStore, useActiveTab, useActiveChatMessages } from '@/lib/store'
import { Send } from 'lucide-react'

const AIAssistant: React.FC = () => {
  const { addChatMessage, setProcessing } = useAppStore()
  const activeTab = useActiveTab()
  const chatMessages = useActiveChatMessages()
  const [chatInput, setChatInput] = useState('')
  // 处理聊天提交
  const handleChatSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!chatInput.trim() || !activeTab) return

    const userMessage = chatInput.trim()
    setChatInput('')
    
    // 添加用户消息
    addChatMessage(activeTab.id, {
      role: 'user',
      content: userMessage
    })

    setProcessing(true)

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage,
          context: {
            originalContent: activeTab.originalContent,
            aiNote: activeTab.aiNoteMarkdown
          }
        })
      })

      if (!response.ok) {
        throw new Error('聊天请求失败')
      }

      const data = await response.json()
      
      // 添加AI回复
      addChatMessage(activeTab.id, {
        role: 'assistant',
        content: data.response
      })
    } catch (error) {
      console.error('聊天错误:', error)
      addChatMessage(activeTab.id, {
        role: 'assistant',
        content: '抱歉，我遇到了一些问题，请稍后再试。'
      })
    } finally {
      setProcessing(false)
    }
  }



  if (!activeTab) {
    return (
      <div className="h-full flex items-center justify-center text-gray-500 p-6">
        <div className="text-center">
          <p className="mb-2">AI助手</p>
          <p className="text-sm">选择或创建一个标签页开始使用</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* AI笔记区域 */}
      <div className="flex-1 flex flex-col">
        {/* 头部 */}
        <div className="p-4 border-b border-gray-200">
          <h3 className="font-semibold text-gray-900">AI笔记</h3>
        </div>

        {/* AI笔记内容 */}
        <div className="flex-1 overflow-auto p-4">
          {activeTab.isLoading ? (
            <div className="flex items-center justify-center h-32">
              <div className="text-center">
                <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                <p className="text-sm text-gray-600">AI正在分析...</p>
              </div>
            </div>
          ) : activeTab.aiNoteMarkdown ? (
            <div className="prose prose-sm max-w-none">
              <ReactMarkdown>{activeTab.aiNoteMarkdown}</ReactMarkdown>
            </div>
          ) : (
            <div className="text-gray-500 text-sm">
              暂无AI笔记
            </div>
          )}
        </div>
      </div>

      {/* 聊天区域 */}
      <div className="border-t border-gray-200 flex flex-col h-80">
        {/* 聊天头部 */}
        <div className="p-3 border-b border-gray-200">
          <h4 className="font-medium text-gray-900">对话</h4>
        </div>

        {/* 聊天消息 */}
        <div className="flex-1 overflow-auto p-3 space-y-3">
          {chatMessages.length === 0 ? (
            <div className="text-gray-500 text-sm text-center py-4">
              开始与AI对话，深入探讨内容
            </div>
          ) : (
            chatMessages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs px-3 py-2 rounded-lg text-sm ${
                    message.role === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}
                >
                  {message.role === 'assistant' ? (
                    <ReactMarkdown className="prose prose-sm max-w-none">
                      {message.content}
                    </ReactMarkdown>
                  ) : (
                    message.content
                  )}
                </div>
              </div>
            ))
          )}
        </div>

        {/* 聊天输入 */}
        <div className="p-3 border-t border-gray-200">
          <form onSubmit={handleChatSubmit} className="flex space-x-2">
            <input
              type="text"
              value={chatInput}
              onChange={(e) => setChatInput(e.target.value)}
              placeholder="问问AI..."
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-sm"
            />
            <button
              type="submit"
              disabled={!chatInput.trim()}
              className="p-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
            >
              <Send size={16} />
            </button>
          </form>
        </div>
      </div>
    </div>
  )
}

export default AIAssistant
