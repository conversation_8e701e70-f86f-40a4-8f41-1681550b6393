'use client'

import React, { useState } from 'react'
import { useAppStore, useActiveTab } from '@/lib/store'
import { X, Plus } from 'lucide-react'
import LightBrowser from '@/components/ui/LightBrowser'

const WorkArea: React.FC = () => {
  const { tabs, activeTabId, setActiveTab, removeTab, addTab, setProcessing } = useAppStore()
  const activeTab = useActiveTab()
  const [inputValue, setInputValue] = useState('')

  // 处理输入提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!inputValue.trim()) return

    setProcessing(true)

    try {
      // 判断输入类型并规范化URL
      let processedInput = inputValue.trim()
      let isUrl = false

      // 检测和规范化URL
      if (processedInput.startsWith('http://') || processedInput.startsWith('https://')) {
        isUrl = true
      } else if (processedInput.includes('.') && !processedInput.includes(' ') && processedInput.length > 3) {
        // 可能是没有协议的URL，自动添加https://
        processedInput = 'https://' + processedInput
        isUrl = true
      }

      if (isUrl) {
        // 对于URL：立即创建标签页并显示浏览器，同时后台处理AI分析
        const tabId = addTab({
          title: new URL(processedInput).hostname,
          sourceType: 'url',
          sourceData: processedInput,
          originalContent: '',
          aiNoteMarkdown: '',
          isLoading: false // 立即显示浏览器，不需要等待
        })

        setInputValue('')
        setProcessing(false)

        // 后台异步处理AI分析
        processUrlInBackground(processedInput, tabId)
      } else {
        // 对于文本：需要等待处理完成
        const tabId = addTab({
          title: '文本内容',
          sourceType: 'text',
          sourceData: inputValue,
          originalContent: '',
          aiNoteMarkdown: '',
          isLoading: true
        })

        // 调用API处理内容
        const response = await fetch('/api/process', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            input: processedInput,
            type: 'text'
          })
        })

        if (!response.ok) {
          throw new Error('处理失败')
        }

        const data = await response.json()

        // 更新标签页内容
        useAppStore.getState().updateTab(tabId, {
          title: data.title || '文本内容',
          originalContent: data.content,
          aiNoteMarkdown: data.aiNote,
          isLoading: false
        })

        setInputValue('')
        setProcessing(false)
      }
    } catch (error) {
      console.error('处理错误:', error)
      setProcessing(false)
      // 可以在这里显示错误提示
    }
  }

  // 后台处理URL的AI分析
  const processUrlInBackground = async (url: string, tabId: string) => {
    try {
      const response = await fetch('/api/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          input: url,
          type: 'url'
        })
      })

      if (response.ok) {
        const data = await response.json()

        // 更新标签页的AI分析结果
        useAppStore.getState().updateTab(tabId, {
          title: data.title || new URL(url).hostname,
          originalContent: data.content,
          aiNoteMarkdown: data.aiNote
        })
      }
    } catch (error) {
      console.error('后台AI分析失败:', error)
    }
  }

  // 如果没有标签页，显示输入界面
  if (tabs.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center bg-white">
        <div className="max-w-2xl w-full px-6">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-light text-gray-900 mb-4">
              开始你的知识沉淀之旅
            </h2>
            <p className="text-gray-600">
              输入网页链接（如 example.com）或粘贴文本内容，网页将立即打开，AI分析在后台进行
            </p>
          </div>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="relative">
              <input
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder="输入网页链接（如 example.com）或粘贴文本内容..."
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-lg"
              />
            </div>
            <button
              type="submit"
              disabled={!inputValue.trim()}
              className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-medium"
            >
              开始处理
            </button>
          </form>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col">
      {/* 标签页栏 */}
      <div className="bg-white border-b border-gray-200 flex items-center px-4">
        <div className="flex-1 flex items-center space-x-1 overflow-x-auto">
          {tabs.map((tab) => (
            <div
              key={tab.id}
              className={`flex items-center space-x-2 px-4 py-2 border-b-2 cursor-pointer transition-colors ${
                tab.id === activeTabId
                  ? 'border-blue-500 text-blue-600 bg-blue-50'
                  : 'border-transparent text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
              onClick={() => setActiveTab(tab.id)}
            >
              <span className="text-sm font-medium truncate max-w-32">
                {tab.title}
              </span>
              {tab.isLoading && (
                <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              )}
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  removeTab(tab.id)
                }}
                className="p-1 hover:bg-gray-200 rounded"
              >
                <X size={14} />
              </button>
            </div>
          ))}
        </div>
        
        {/* 新建标签页按钮 */}
        <button
          onClick={() => {
            // 这里可以实现新建空白标签页或显示输入框
            setInputValue('')
          }}
          className="p-2 hover:bg-gray-100 rounded"
        >
          <Plus size={16} />
        </button>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 overflow-hidden bg-white">
        {activeTab ? (
          activeTab.isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-gray-600">正在处理内容...</p>
              </div>
            </div>
          ) : activeTab.sourceType === 'url' ? (
            // 对于URL类型，显示轻量级浏览器
            <LightBrowser
              url={activeTab.sourceData}
              title={activeTab.title}
              onLoadComplete={() => {
                console.log('页面加载完成')
              }}
              onError={(error) => {
                console.error('页面加载错误:', error)
              }}
            />
          ) : (
            // 对于文本类型，显示处理后的内容
            <div className="h-full overflow-auto">
              <div className="max-w-4xl mx-auto px-6 py-8">
                <div className="prose prose-lg max-w-none">
                  <h1 className="text-2xl font-bold text-gray-900 mb-6">
                    {activeTab.title}
                  </h1>
                  <div
                    className="text-gray-700 leading-relaxed"
                    dangerouslySetInnerHTML={{ __html: activeTab.originalContent }}
                  />
                </div>
              </div>
            </div>
          )
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            选择一个标签页查看内容
          </div>
        )}
      </div>
    </div>
  )
}

export default WorkArea
