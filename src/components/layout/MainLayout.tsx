'use client'

import React from 'react'
import { useAppStore } from '@/lib/store'
import WorkArea from './WorkArea'
import AIAssistant from './AIAssistant'

const MainLayout: React.FC = () => {
  const { tabs } = useAppStore()

  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      {/* 顶部导航栏 */}
      <header className="bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h1 className="text-xl font-semibold text-gray-900">沉淀</h1>
        </div>
        <div className="text-sm text-gray-500">
          {tabs.length > 0 && `${tabs.length} 个标签页`}
        </div>
      </header>

      {/* 主要内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 主工作区 */}
        <div className="flex-1 flex flex-col">
          <WorkArea />
        </div>

        {/* AI助手面板 - 只在有标签页时显示 */}
        {tabs.length > 0 && (
          <div className="w-96 border-l border-gray-200 bg-white">
            <AIAssistant />
          </div>
        )}
      </div>
    </div>
  )
}

export default MainLayout
