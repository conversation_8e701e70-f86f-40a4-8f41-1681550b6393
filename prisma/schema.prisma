// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 会话模型 - 根据PRD定义
model Session {
  id                 String   @id @default(cuid())
  title              String   // 从原文<title>或URL生成
  sourceType         String   // "url" or "text"
  sourceData         String   // URL链接或原文的标识
  originalContent    String   // 抓取并清洗后的原文HTML或文本
  aiNoteMarkdown     String   // AI生成的、唯一的、结构化的Markdown笔记
  createdAt          DateTime @default(now())

  @@map("sessions")
}
